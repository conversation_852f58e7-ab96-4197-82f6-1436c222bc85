#!/usr/bin/env python3
"""
Test script to verify the fixes for pandas import and markdown containers
"""

import sys
import os

def test_pandas_import():
    """Test if pandas can be imported correctly"""
    print("🧪 Testing pandas import...")
    
    try:
        import pandas as pd
        print("✅ Pandas imported successfully")
        
        # Test DataFrame creation
        df = pd.DataFrame({'test': [1, 2, 3]})
        print("✅ DataFrame creation works")
        
        return True
    except Exception as e:
        print(f"❌ Pandas test failed: {e}")
        return False

def test_app_syntax():
    """Test if the app.py file has correct syntax"""
    print("\n🧪 Testing app.py syntax...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Compile to check syntax
        compile(content, 'app.py', 'exec')
        print("✅ App.py syntax is correct")
        
        return True
    except SyntaxError as e:
        print(f"❌ Syntax error in app.py: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading app.py: {e}")
        return False

def test_markdown_containers():
    """Test if markdown containers are properly formatted"""
    print("\n🧪 Testing markdown containers...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for properly formatted main headers
        main_header_patterns = [
            '<div class="main-header">',
            '<h1>',
            '<p>',
            '</div>'
        ]
        
        issues = []
        
        # Count opening and closing divs
        opening_divs = content.count('<div class="')
        closing_divs = content.count('</div>')
        
        if opening_divs != closing_divs:
            issues.append(f"Mismatched div tags: {opening_divs} opening, {closing_divs} closing")
        
        # Check for empty containers (opening div immediately followed by closing div)
        import re
        empty_containers = re.findall(r'<div[^>]*>\s*</div>', content)
        if empty_containers:
            issues.append(f"Found {len(empty_containers)} empty containers")
        
        # Check for proper main header structure
        main_headers = re.findall(r'<div class="main-header">(.*?)</div>', content, re.DOTALL)
        for header in main_headers:
            if '<h1>' not in header or '<p>' not in header:
                issues.append("Main header missing h1 or p tags")
        
        if issues:
            for issue in issues:
                print(f"⚠️ {issue}")
            return False
        else:
            print("✅ Markdown containers are properly formatted")
            return True
            
    except Exception as e:
        print(f"❌ Error testing markdown containers: {e}")
        return False

def test_imports():
    """Test if all required imports work"""
    print("\n🧪 Testing all imports...")
    
    required_imports = [
        'streamlit',
        'pandas',
        'bcrypt',
        'plotly.express',
        'joblib',
        'fpdf'
    ]
    
    failed_imports = []
    
    for module in required_imports:
        try:
            __import__(module)
            print(f"✅ {module} imported successfully")
        except ImportError:
            failed_imports.append(module)
            print(f"❌ {module} import failed")
    
    return len(failed_imports) == 0

def main():
    """Main test function"""
    print("🔧 Cultural Heritage System - Fix Verification")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # Test pandas import
    if test_pandas_import():
        tests_passed += 1
    
    # Test app syntax
    if test_app_syntax():
        tests_passed += 1
    
    # Test markdown containers
    if test_markdown_containers():
        tests_passed += 1
    
    # Test all imports
    if test_imports():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All fixes verified successfully!")
        print("✅ Pandas import error fixed")
        print("✅ Markdown containers properly formatted")
        print("✅ Application syntax is correct")
        print("🚀 The application should run without errors")
    else:
        print("⚠️ Some issues remain. Please check the failed tests above.")
        
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
