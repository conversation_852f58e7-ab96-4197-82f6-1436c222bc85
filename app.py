import streamlit as st
import pandas as pd
import bcrypt
from db import get_all_sites, insert_site, get_user, get_site_statistics, get_all_users, add_user, delete_user, update_password
import plotly.express as px
from io import BytesIO
from fpdf import FPDF
import joblib  # Import joblib for loading the ML model
import os  # Import os to check file existence

# --- Page Configuration ---
st.set_page_config(
    page_title="Cultural Heritage System",
    layout="wide",
    page_icon="🏛️",
    initial_sidebar_state="expanded"
)

# --- Custom CSS for Professional Styling ---
st.markdown("""
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    /* Global Styles */
    .main {
        font-family: 'Inter', sans-serif;
    }

    /* Header Styling */
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .main-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .main-header p {
        font-size: 1.2rem;
        font-weight: 300;
        opacity: 0.9;
    }

    /* Enhanced Sidebar Styling */
    .css-1d391kg {
        background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
    }

    /* Sidebar Navigation Items */
    .nav-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        margin: 0.25rem 0;
        border-radius: 8px;
        transition: all 0.3s ease;
        cursor: pointer;
        background: white;
        border: 1px solid #e9ecef;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .nav-item:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateX(5px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .nav-item.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: #667eea;
    }

    .nav-icon {
        font-size: 1.2rem;
        margin-right: 0.75rem;
        min-width: 24px;
    }

    .nav-text {
        font-weight: 500;
        font-size: 0.95rem;
    }

    .nav-badge {
        background: #dc3545;
        color: white;
        font-size: 0.7rem;
        padding: 2px 6px;
        border-radius: 10px;
        margin-left: auto;
    }

    /* Sidebar User Card Enhancement */
    .sidebar-user-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1.5rem;
        border-radius: 15px;
        margin-bottom: 1.5rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
        color: white;
        border: 2px solid rgba(255,255,255,0.3);
    }

    .user-name {
        color: white;
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0;
    }

    .user-role {
        color: rgba(255,255,255,0.8);
        font-size: 0.85rem;
        margin: 0.25rem 0 0 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .user-status {
        display: inline-flex;
        align-items: center;
        background: rgba(255,255,255,0.2);
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        color: white;
        margin-top: 0.75rem;
    }

    .status-dot {
        width: 8px;
        height: 8px;
        background: #28a745;
        border-radius: 50%;
        margin-right: 0.5rem;
    }

    /* Card Styling */
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #667eea;
        margin-bottom: 1rem;
    }

    /* Button Styling */
    .stButton > button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    /* Form Styling */
    .stTextInput > div > div > input,
    .stSelectbox > div > div > select,
    .stTextArea > div > div > textarea,
    .stNumberInput > div > div > input {
        border-radius: 8px;
        border: 2px solid #e9ecef;
        transition: border-color 0.3s ease;
    }

    .stTextInput > div > div > input:focus,
    .stSelectbox > div > div > select:focus,
    .stTextArea > div > div > textarea:focus,
    .stNumberInput > div > div > input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* Success/Error Message Styling */
    .stSuccess {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 8px;
        padding: 1rem;
    }

    .stError {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
        padding: 1rem;
    }

    /* Info Box Styling */
    .info-box {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #2196f3;
        margin: 1rem 0;
    }

    /* Chart Container */
    .chart-container {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
    }

    /* Login Form Styling */
    .login-container {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    /* Hide Streamlit Branding */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}

    /* Custom spacing */
    .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
</style>
""", unsafe_allow_html=True)

# --- Session Defaults ---
if "logged_in" not in st.session_state:
    st.session_state.logged_in = False
if "show_login" not in st.session_state:
    st.session_state.show_login = False

# --- Authentication ---
def login_form():
    st.markdown('<div class="login-container">', unsafe_allow_html=True)
    st.markdown("### 🔐 Login to Your Account")
    st.markdown("Welcome back! Please enter your credentials to access the Cultural Heritage System.")

    with st.form("login_form"):
        col1, col2 = st.columns([1, 3])
        with col1:
            st.markdown("")  # Empty space for layout
        with col2:
            username = st.text_input(
                "👤 Username",
                placeholder="Enter your username",
                help="Enter your registered username"
            )
            password = st.text_input(
                "🔒 Password",
                type="password",
                placeholder="Enter your password",
                help="Enter your account password"
            )

            st.markdown("<br>", unsafe_allow_html=True)
            login_btn = st.form_submit_button("🚀 Login", use_container_width=True)

        if login_btn:
            if not username or not password:
                st.error("⚠️ Please fill in both username and password")
            else:
                with st.spinner("Authenticating..."):
                    user = get_user(username)
                    if user and bcrypt.checkpw(password.encode(), user['password_hash'].encode()):
                        st.session_state.logged_in = True
                        st.session_state.username = user['username']
                        st.session_state.role = user['role']
                        st.success(f"✅ Welcome back, {user['username']}!")
                        st.rerun()  # Refresh to show the dashboard
                    else:
                        st.error("❌ Invalid username or password. Please try again.")

    st.markdown('</div>', unsafe_allow_html=True)

# --- Load AI/ML Model ---
@st.cache_resource
def load_model():
    """Load the AI/ML model with caching for better performance"""
    model_path = "models/risk_predictor_model.joblib"
    try:
        if os.path.exists(model_path):
            model = joblib.load(model_path)
            return model, None
        else:
            error_msg = f"Model file '{model_path}' not found. Please ensure the model exists."
            return None, error_msg
    except Exception as e:
        error_msg = f"Error loading model: {str(e)}"
        return None, error_msg

# Load the model
model, model_error = load_model()

# --- Enhanced AI Risk Predictor ---
def predict_risk(lat, lon, climate_factor, age, material_type, natural_disaster_exposure, legal_protection_status, maintenance_frequency):
    """
    Predict risk level for a cultural heritage site using AI/ML model
    Returns risk level and confidence score
    """
    if model is None:
        return "Error: Model not available", 0.0

    try:
        # Prepare input features for the model
        features = [[
            lat, lon, climate_factor, age,
            material_type,  # Assume material_type is encoded numerically
            natural_disaster_exposure,  # Assume this is encoded as Low=0, Medium=1, High=2
            int(legal_protection_status),  # Convert boolean to integer (True=1, False=0)
            maintenance_frequency
        ]]

        # Get prediction and probability
        risk_level = model.predict(features)[0]
        probabilities = model.predict_proba(features)[0] if hasattr(model, 'predict_proba') else [0.5]
        confidence = max(probabilities) * 100

        # Map numeric prediction to text if needed
        risk_mapping = {0: "Low", 1: "Medium", 2: "High"}
        if isinstance(risk_level, (int, float)) and risk_level in risk_mapping:
            risk_level = risk_mapping[risk_level]

        return risk_level, confidence
    except Exception as e:
        return f"Error in prediction: {str(e)}", 0.0

# --- AI-Powered Recommendation Engine ---
def generate_recommendations(risk_level, climate_factor, age, material_type_encoded,
                           natural_disaster_exposure, legal_protection_status, maintenance_frequency):
    """
    Generate AI-powered recommendations for heritage site preservation
    Based on risk factors and site characteristics
    """
    recommendations = {
        "immediate": [],
        "short_term": [],
        "long_term": [],
        "preventive": [],
        "monitoring": []
    }

    # Risk-based recommendations
    if risk_level == "High":
        recommendations["immediate"].extend([
            "🚨 Conduct emergency structural assessment within 30 days",
            "🛡️ Implement immediate protective measures to prevent further deterioration",
            "📞 Contact heritage conservation specialists for urgent consultation",
            "🚧 Restrict public access if safety concerns exist"
        ])
    elif risk_level == "Medium":
        recommendations["immediate"].extend([
            "🔍 Schedule comprehensive site inspection within 90 days",
            "📋 Develop detailed conservation management plan"
        ])

    # Age-based recommendations
    if age > 1000:
        recommendations["preventive"].extend([
            "🏛️ Implement specialized ancient heritage preservation protocols",
            "🔬 Conduct advanced material analysis using non-invasive techniques",
            "📚 Document site using 3D scanning and photogrammetry"
        ])
    elif age > 500:
        recommendations["preventive"].extend([
            "🏗️ Apply historical building conservation standards",
            "🧪 Regular material degradation monitoring"
        ])
    elif age > 100:
        recommendations["preventive"].extend([
            "🔧 Standard heritage maintenance protocols",
            "📸 Annual photographic documentation"
        ])

    # Climate factor recommendations
    if climate_factor > 7:
        recommendations["immediate"].extend([
            "🌡️ Install climate monitoring systems",
            "💨 Implement advanced weatherproofing measures"
        ])
        recommendations["long_term"].extend([
            "🏠 Consider protective shelter construction",
            "🌿 Establish vegetation barriers for wind protection"
        ])
    elif climate_factor > 4:
        recommendations["short_term"].extend([
            "🌦️ Enhance weather protection systems",
            "🌡️ Install basic climate monitoring"
        ])

    # Material-specific recommendations
    material_recommendations = {
        0: {  # Stone
            "preventive": ["🪨 Apply stone consolidation treatments", "💧 Improve drainage systems"],
            "monitoring": ["🔍 Monitor for stone decay and weathering", "🧪 Test for salt damage"]
        },
        1: {  # Coral
            "preventive": ["🐚 Apply marine-grade protective coatings", "🌊 Implement coastal erosion protection"],
            "monitoring": ["🌊 Monitor sea level changes", "🐠 Assess marine ecosystem impact"]
        },
        2: {  # Brick
            "preventive": ["🧱 Repoint mortar joints regularly", "💧 Improve water management"],
            "monitoring": ["🔍 Check for brick spalling", "💧 Monitor moisture levels"]
        },
        3: {  # Wood
            "preventive": ["🪵 Apply wood preservation treatments", "🐛 Implement pest control measures"],
            "monitoring": ["🐛 Regular termite and pest inspections", "💧 Monitor humidity levels"]
        },
        4: {  # Iron
            "preventive": ["🔧 Apply anti-corrosion treatments", "🎨 Maintain protective paint coatings"],
            "monitoring": ["🦠 Monitor for rust and corrosion", "🌧️ Check drainage around metal elements"]
        }
    }

    if material_type_encoded in material_recommendations:
        mat_rec = material_recommendations[material_type_encoded]
        recommendations["preventive"].extend(mat_rec.get("preventive", []))
        recommendations["monitoring"].extend(mat_rec.get("monitoring", []))

    # Disaster exposure recommendations
    if natural_disaster_exposure >= 2:  # High
        recommendations["immediate"].extend([
            "🌪️ Develop emergency disaster response plan",
            "📦 Create emergency conservation kit"
        ])
        recommendations["long_term"].extend([
            "🏗️ Implement disaster-resistant structural reinforcements",
            "📡 Install early warning systems"
        ])
    elif natural_disaster_exposure == 1:  # Medium
        recommendations["short_term"].extend([
            "⚠️ Develop basic disaster preparedness plan",
            "🎒 Prepare emergency response procedures"
        ])

    # Legal protection recommendations
    if not legal_protection_status:
        recommendations["immediate"].extend([
            "⚖️ Apply for heritage protection status immediately",
            "📄 Document historical significance for legal protection"
        ])
        recommendations["short_term"].extend([
            "🏛️ Engage with local heritage authorities",
            "📋 Prepare heritage impact assessments"
        ])

    # Maintenance frequency recommendations
    if maintenance_frequency < 1:
        recommendations["immediate"].extend([
            "🔧 Establish regular maintenance schedule (minimum quarterly)",
            "👷 Train local staff in basic conservation techniques"
        ])
    elif maintenance_frequency < 2:
        recommendations["short_term"].extend([
            "📅 Increase maintenance frequency to bi-annual minimum",
            "📋 Develop detailed maintenance checklists"
        ])

    # General monitoring recommendations
    recommendations["monitoring"].extend([
        "📊 Implement digital monitoring systems",
        "📱 Use mobile apps for condition reporting",
        "🤖 Consider IoT sensors for continuous monitoring",
        "📈 Establish baseline condition metrics"
    ])

    # Long-term sustainability
    recommendations["long_term"].extend([
        "🎓 Develop community education programs",
        "💰 Establish sustainable funding mechanisms",
        "🌍 Create tourism management plans",
        "🔬 Partner with research institutions"
    ])

    return recommendations

# --- Enhanced User Management ---
def user_management():
    st.markdown('<div class="main-header">', unsafe_allow_html=True)
    st.markdown('<h1>👥 User Management</h1>', unsafe_allow_html=True)
    st.markdown('<p>Manage system users and their permissions</p>', unsafe_allow_html=True)
    st.markdown('</div>', unsafe_allow_html=True)

    users_df = get_all_users()  # Fetch all users from the database as a DataFrame

    if not users_df.empty:
        st.markdown("### 📋 Current Users")

        # Create a more user-friendly display
        for _, user in users_df.iterrows():
            col1, col2, col3, col4 = st.columns([3, 2, 2, 1])

            with col1:
                st.markdown(f"**👤 {user['username']}**")
                st.markdown(f"📧 {user['email']}")

            with col2:
                role_color = "#28a745" if user['role'] == 'admin' else "#007bff"
                st.markdown(f'<span style="background-color: {role_color}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">{user["role"].upper()}</span>', unsafe_allow_html=True)

            with col3:
                st.markdown("🟢 Active")

            with col4:
                if st.button("🗑️", key=f"delete_{user['username']}", help=f"Delete user {user['username']}"):
                    if user['username'] != st.session_state.username:  # Prevent self-deletion
                        try:
                            delete_user(user['username'])
                            st.success(f"✅ User '{user['username']}' deleted successfully!")
                            st.rerun()
                        except Exception as e:
                            st.error(f"❌ Error deleting user: {str(e)}")
                    else:
                        st.error("❌ You cannot delete your own account!")

            st.markdown("---")
    else:
        st.info("📭 No users found in the system.")

    # Add New User Form
    st.markdown("### ➕ Add New User")
    st.markdown('<div class="metric-card">', unsafe_allow_html=True)

    with st.form("add_user_form"):
        col1, col2 = st.columns(2)

        with col1:
            username = st.text_input(
                "👤 Username",
                placeholder="Enter username",
                help="Choose a unique username for the new user"
            )
            password = st.text_input(
                "🔒 Password",
                type="password",
                placeholder="Enter secure password",
                help="Password should be at least 8 characters long"
            )

        with col2:
            email = st.text_input(
                "📧 Email",
                placeholder="<EMAIL>",
                help="Enter a valid email address"
            )
            role = st.selectbox(
                "👑 Role",
                ["user", "admin"],
                help="Select user role: 'user' for regular access, 'admin' for full access"
            )

        st.markdown("<br>", unsafe_allow_html=True)
        add_user_btn = st.form_submit_button("🚀 Add User", use_container_width=True)

        if add_user_btn:
            # Validation
            if not all([username, password, role, email]):
                st.error("⚠️ Please fill in all fields.")
            elif len(password) < 8:
                st.error("⚠️ Password must be at least 8 characters long.")
            elif "@" not in email or "." not in email:
                st.error("⚠️ Please enter a valid email address.")
            else:
                try:
                    password_hash = bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()
                    add_user(username, password_hash, role, email)
                    st.success(f"✅ User '{username}' added successfully!")
                    st.rerun()
                except Exception as e:
                    if "duplicate key value violates unique constraint" in str(e):
                        st.error(f"❌ Username '{username}' already exists. Please choose a different username.")
                    else:
                        st.error(f"❌ An unexpected error occurred: {str(e)}")

    st.markdown('</div>', unsafe_allow_html=True)

# --- Enhanced Password Management ---
def password_management():
    st.markdown('<div class="main-header">', unsafe_allow_html=True)
    st.markdown('<h1>🔒 Password Management</h1>', unsafe_allow_html=True)
    st.markdown('<p>Update your account password securely</p>', unsafe_allow_html=True)
    st.markdown('</div>', unsafe_allow_html=True)

    st.markdown('<div class="metric-card">', unsafe_allow_html=True)

    with st.form("reset_password_form"):
        st.markdown("### 🔄 Change Your Password")
        st.markdown("For security reasons, please enter your current password to confirm your identity.")

        current_password = st.text_input(
            "🔒 Current Password",
            type="password",
            placeholder="Enter your current password",
            help="Enter your existing password for verification"
        )

        col1, col2 = st.columns(2)
        with col1:
            new_password = st.text_input(
                "🆕 New Password",
                type="password",
                placeholder="Enter new password",
                help="Password should be at least 8 characters long"
            )
        with col2:
            confirm_password = st.text_input(
                "✅ Confirm New Password",
                type="password",
                placeholder="Confirm new password",
                help="Re-enter your new password"
            )

        st.markdown("<br>", unsafe_allow_html=True)
        reset_password_btn = st.form_submit_button("🔄 Update Password", use_container_width=True)

        if reset_password_btn:
            # Validation
            if not all([current_password, new_password, confirm_password]):
                st.error("⚠️ Please fill in all password fields.")
            elif len(new_password) < 8:
                st.error("⚠️ New password must be at least 8 characters long.")
            elif new_password != confirm_password:
                st.error("❌ New passwords do not match.")
            else:
                try:
                    user = get_user(st.session_state.username)
                    if user and bcrypt.checkpw(current_password.encode(), user['password_hash'].encode()):
                        new_password_hash = bcrypt.hashpw(new_password.encode(), bcrypt.gensalt()).decode()
                        update_password(st.session_state.username, new_password_hash)
                        st.success("✅ Password updated successfully!")
                    else:
                        st.error("❌ Current password is incorrect.")
                except Exception as e:
                    st.error(f"❌ Error updating password: {str(e)}")

    st.markdown('</div>', unsafe_allow_html=True)

# --- Export Data Functions ---
def export_to_excel(dataframe, filename="report.xlsx"):
    output = BytesIO()
    with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
        dataframe.to_excel(writer, index=False, sheet_name="Report")
        writer.close()  # Use close() instead of save()
    st.download_button(
        label="📥 Export to Excel",
        data=output.getvalue(),
        file_name=filename,
        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    )

def export_to_pdf(dataframe, filename="report.pdf"):
    pdf = FPDF()
    pdf.add_page()
    pdf.set_font("Arial", size=12)
    pdf.cell(200, 10, txt="Analytics Report", ln=True, align="C")
    pdf.ln(10)
    for col in dataframe.columns:
        pdf.cell(40, 10, col, border=1)
    pdf.ln()
    for _, row in dataframe.iterrows():
        for value in row:
            pdf.cell(40, 10, str(value), border=1)
        pdf.ln()
    output = BytesIO()
    pdf_content = pdf.output(dest="S").encode("latin1")  # Write PDF content to a string
    output.write(pdf_content)  # Write the string to BytesIO
    st.download_button(
        label="📥 Export to PDF",
        data=output.getvalue(),
        file_name=filename,
        mime="application/pdf",
    )

# --- Enhanced Dashboard UI ---
def dashboard():
    # Enhanced Sidebar User Card
    user_initial = st.session_state['username'][0].upper()
    st.sidebar.markdown(f"""
    <div class="sidebar-user-card">
        <div class="user-avatar">{user_initial}</div>
        <h3 class="user-name">{st.session_state['username']}</h3>
        <p class="user-role">{st.session_state['role']}</p>
        <div class="user-status">
            <div class="status-dot"></div>
            Online
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Navigation Menu
    st.sidebar.markdown("### 🧭 Navigation")

    # Define menu items with icons and descriptions
    menu_items = [
        {"icon": "🏛️", "text": "View Sites", "desc": "Browse heritage sites"},
        {"icon": "🧠", "text": "AI Risk Assessment", "desc": "Predict & analyze risks"},
        {"icon": "📊", "text": "Analytics & Reports", "desc": "Data insights"},
        {"icon": "🔒", "text": "Password Management", "desc": "Update credentials"},
        {"icon": "🚪", "text": "Logout", "desc": "Sign out"}
    ]

    if st.session_state['role'] == "admin":
        menu_items.insert(1, {"icon": "➕", "text": "Add New Site", "desc": "Register new site"})
        menu_items.insert(2, {"icon": "👥", "text": "User Management", "desc": "Manage users"})

    # Create custom navigation
    selected_menu = None
    for i, item in enumerate(menu_items):
        if st.sidebar.button(
            f"{item['icon']} {item['text']}",
            key=f"nav_{i}",
            help=item['desc'],
            use_container_width=True
        ):
            selected_menu = item['text']

    # Store selected menu in session state
    if selected_menu:
        st.session_state.current_menu = selected_menu

    # Get current menu (default to first item if not set)
    menu = st.session_state.get('current_menu', menu_items[0]['text'])

    # System Status Section
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 🔧 System Status")

    # Model status
    if model_error:
        st.sidebar.error(f"🚨 AI Model: Offline")
        st.sidebar.caption(f"Error: {model_error[:50]}...")
    else:
        st.sidebar.success("🤖 AI Model: Online")

    # Database status
    try:
        from db import get_db_connection
        conn = get_db_connection()
        conn.close()
        st.sidebar.success("🗄️ Database: Connected")
    except:
        st.sidebar.error("🗄️ Database: Error")

    # Quick stats
    try:
        sites_df = get_all_sites()
        site_count = len(sites_df) if not sites_df.empty else 0
        st.sidebar.info(f"📍 Total Sites: {site_count}")
    except:
        st.sidebar.info("📍 Total Sites: N/A")

    if menu == "View Sites":
        st.markdown('<div class="main-header">', unsafe_allow_html=True)
        st.markdown('<h1>🏛️ Cultural Heritage Sites</h1>', unsafe_allow_html=True)
        st.markdown('<p>Explore and discover registered cultural heritage sites</p>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)

        sites_df = get_all_sites()
        if not sites_df.empty:
            # Display metrics
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.markdown('<div class="metric-card">', unsafe_allow_html=True)
                st.metric("Total Sites", len(sites_df))
                st.markdown('</div>', unsafe_allow_html=True)

            with col2:
                st.markdown('<div class="metric-card">', unsafe_allow_html=True)
                regions = sites_df['region'].nunique() if 'region' in sites_df.columns else 0
                st.metric("Regions", regions)
                st.markdown('</div>', unsafe_allow_html=True)

            with col3:
                st.markdown('<div class="metric-card">', unsafe_allow_html=True)
                site_types = sites_df['site_type'].nunique() if 'site_type' in sites_df.columns else 0
                st.metric("Site Types", site_types)
                st.markdown('</div>', unsafe_allow_html=True)

            with col4:
                st.markdown('<div class="metric-card">', unsafe_allow_html=True)
                latest_site = sites_df.iloc[0]['name'] if 'name' in sites_df.columns else "N/A"
                st.metric("Latest Site", latest_site[:15] + "..." if len(latest_site) > 15 else latest_site)
                st.markdown('</div>', unsafe_allow_html=True)

            # Map visualization
            st.markdown("### 🗺️ Site Locations")
            st.markdown('<div class="chart-container">', unsafe_allow_html=True)
            if 'latitude' in sites_df.columns and 'longitude' in sites_df.columns:
                map_df = sites_df.rename(columns={'latitude': 'lat', 'longitude': 'lon'})
                st.map(map_df)
            st.markdown('</div>', unsafe_allow_html=True)

            # Sites table
            st.markdown("### 📋 Sites Details")
            st.markdown('<div class="chart-container">', unsafe_allow_html=True)
            # Remove site_id if it exists and show a clean table
            display_df = sites_df.copy()
            if 'site_id' in display_df.columns:
                display_df = display_df.drop(columns=["site_id"])
            st.dataframe(display_df, use_container_width=True)
            st.markdown('</div>', unsafe_allow_html=True)
        else:
            st.markdown('<div class="info-box">', unsafe_allow_html=True)
            st.info("📭 No cultural heritage sites found. Add some sites to get started!")
            st.markdown('</div>', unsafe_allow_html=True)

    elif menu == "Add New Site":
        st.markdown('<div class="main-header">', unsafe_allow_html=True)
        st.markdown('<h1>➕ Add New Heritage Site</h1>', unsafe_allow_html=True)
        st.markdown('<p>Register a new cultural heritage site in the system</p>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)

        st.markdown('<div class="metric-card">', unsafe_allow_html=True)
        with st.form("site_form"):
            st.markdown("### 📝 Site Information")

            col1, col2 = st.columns(2)
            with col1:
                name = st.text_input(
                    "🏛️ Site Name",
                    placeholder="Enter the site name",
                    help="Official name of the cultural heritage site"
                )
                lat = st.number_input(
                    "🌍 Latitude",
                    format="%.6f",
                    help="Latitude coordinate (e.g., -1.286389 for Nairobi)"
                )
                region = st.selectbox(
                    "📍 Region",
                    ["Coast", "Central", "Nairobi", "Western", "Eastern", "Nyanza", "Rift Valley", "North Eastern"],
                    help="Select the geographical region"
                )

            with col2:
                site_type = st.text_input(
                    "🏗️ Site Type",
                    placeholder="e.g., Archaeological, Historical, Monument",
                    help="Type or category of the heritage site"
                )
                lon = st.number_input(
                    "🌍 Longitude",
                    format="%.6f",
                    help="Longitude coordinate (e.g., 36.817223 for Nairobi)"
                )

            description = st.text_area(
                "📄 Description",
                placeholder="Provide a detailed description of the site...",
                help="Detailed description including historical significance, features, etc.",
                height=100
            )

            st.markdown("<br>", unsafe_allow_html=True)
            submitted = st.form_submit_button("🚀 Add Site", use_container_width=True)

            if submitted:
                if not name or not lat or not lon:
                    st.error("⚠️ Please fill in all required fields (Name, Latitude, Longitude).")
                elif lat < -90 or lat > 90:
                    st.error("⚠️ Latitude must be between -90 and 90 degrees.")
                elif lon < -180 or lon > 180:
                    st.error("⚠️ Longitude must be between -180 and 180 degrees.")
                else:
                    try:
                        insert_site(name, lat, lon, region, site_type, description)
                        st.success(f"✅ Site '{name}' added successfully!")
                        st.balloons()
                    except Exception as e:
                        st.error(f"❌ Error adding site: {str(e)}")

        st.markdown('</div>', unsafe_allow_html=True)

    elif menu == "User Management" and st.session_state['role'] == "admin":
        user_management()

    elif menu == "AI Risk Assessment":
        st.markdown('<div class="main-header">', unsafe_allow_html=True)
        st.markdown('<h1>🧠 AI Risk Assessment</h1>', unsafe_allow_html=True)
        st.markdown('<p>Predict risk levels and get AI-powered preservation recommendations</p>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)

        # Create tabs for Risk Prediction and Recommendations
        tab1, tab2 = st.tabs(["🔮 Risk Prediction", "💡 AI Recommendations"])

        with tab1:
            if model_error:
                st.error(f"❌ Cannot perform predictions: {model_error}")
                st.info("💡 Please ensure the AI model is properly loaded to use this feature.")
            else:
                st.markdown('<div class="metric-card">', unsafe_allow_html=True)
                with st.form("risk_form"):
                    st.markdown("### 📊 Site Assessment Parameters")
                    st.markdown("Fill in the details below to get an AI-powered risk assessment for the heritage site.")

                    # Location Information
                    st.markdown("#### 🌍 Location Information")
                    col1, col2 = st.columns(2)
                    with col1:
                        lat = st.number_input(
                            "Latitude",
                            format="%.6f",
                            key="risk_lat",
                            help="Latitude coordinate of the site"
                        )
                    with col2:
                        lon = st.number_input(
                            "Longitude",
                            format="%.6f",
                            key="risk_lon",
                            help="Longitude coordinate of the site"
                        )

                    # Environmental Factors
                    st.markdown("#### 🌡️ Environmental Factors")
                    col1, col2 = st.columns(2)
                    with col1:
                        climate_factor = st.number_input(
                            "Climate Factor",
                            format="%.2f",
                            key="risk_climate",
                            min_value=0.0,
                            max_value=10.0,
                            help="Climate impact factor (0-10 scale)"
                        )
                    with col2:
                        natural_disaster_exposure = st.selectbox(
                            "Natural Disaster Exposure",
                            ["Low", "Medium", "High"],
                            key="risk_disaster",
                            help="Level of exposure to natural disasters"
                        )

                    # Site Characteristics
                    st.markdown("#### 🏛️ Site Characteristics")
                    col1, col2 = st.columns(2)
                    with col1:
                        age = st.number_input(
                            "Age of Site (years)",
                            key="risk_age",
                            min_value=1,
                            help="Age of the heritage site in years"
                        )
                        material_type = st.selectbox(
                            "Primary Material",
                            ["Stone", "Coral", "Brick", "Wood", "Iron"],
                            key="risk_material",
                            help="Primary construction material of the site"
                        )

                    with col2:
                        legal_protection_status = st.selectbox(
                            "Legal Protection Status",
                            [True, False],
                            format_func=lambda x: "Protected" if x else "Not Protected",
                            key="risk_legal",
                            help="Whether the site has legal protection status"
                        )
                        maintenance_frequency = st.number_input(
                            "Maintenance Frequency (per year)",
                            min_value=0,
                            key="risk_maintenance",
                            help="Number of maintenance activities per year"
                        )

                    st.markdown("<br>", unsafe_allow_html=True)
                    predict_btn = st.form_submit_button("🔮 Predict Risk Level", use_container_width=True)

                    if predict_btn:
                        # Validation
                        if lat == 0.0 and lon == 0.0:
                            st.error("⚠️ Please enter valid latitude and longitude coordinates.")
                        elif age <= 0:
                            st.error("⚠️ Please enter a valid age for the site.")
                        else:
                            with st.spinner("🤖 AI is analyzing the site..."):
                                # Map categorical values to numerical values for prediction
                                material_type_mapping = {"Stone": 0, "Coral": 1, "Brick": 2, "Wood": 3, "Iron": 4}
                                disaster_mapping = {"Low": 0, "Medium": 1, "High": 2}

                                material_type_encoded = material_type_mapping[material_type]
                                disaster_exposure_encoded = disaster_mapping[natural_disaster_exposure]

                                # Get prediction
                                risk, confidence = predict_risk(
                                    lat, lon, climate_factor, age,
                                    material_type_encoded, disaster_exposure_encoded,
                                    legal_protection_status, maintenance_frequency
                                )

                                # Store prediction results in session state for recommendations tab
                                st.session_state.prediction_results = {
                                    'risk': risk,
                                    'confidence': confidence,
                                    'climate_factor': climate_factor,
                                    'age': age,
                                    'material_type_encoded': material_type_encoded,
                                    'material_type_text': material_type,
                                    'natural_disaster_exposure': disaster_exposure_encoded,
                                    'disaster_text': natural_disaster_exposure,
                                    'legal_protection_status': legal_protection_status,
                                    'maintenance_frequency': maintenance_frequency
                                }

                                # Display results
                                if "Error" in str(risk):
                                    st.error(f"❌ {risk}")
                                else:
                                    st.markdown("### 🎯 Risk Assessment Results")

                                    # Risk level with color coding
                                    risk_colors = {"Low": "#28a745", "Medium": "#ffc107", "High": "#dc3545"}
                                    risk_color = risk_colors.get(str(risk), "#6c757d")

                                    col1, col2, col3 = st.columns(3)
                                    with col1:
                                        st.markdown(f"""
                                        <div style="text-align: center; padding: 1rem; background-color: {risk_color}; color: white; border-radius: 10px;">
                                            <h2 style="margin: 0;">🛡️ Risk Level</h2>
                                            <h1 style="margin: 0;">{risk}</h1>
                                        </div>
                                        """, unsafe_allow_html=True)

                                    with col2:
                                        st.markdown(f"""
                                        <div style="text-align: center; padding: 1rem; background-color: #17a2b8; color: white; border-radius: 10px;">
                                            <h2 style="margin: 0;">📊 Confidence</h2>
                                            <h1 style="margin: 0;">{confidence:.1f}%</h1>
                                        </div>
                                        """, unsafe_allow_html=True)

                                    with col3:
                                        recommendation = "Monitor regularly" if risk == "Low" else "Immediate attention needed" if risk == "High" else "Enhanced monitoring"
                                        st.markdown(f"""
                                        <div style="text-align: center; padding: 1rem; background-color: #6f42c1; color: white; border-radius: 10px;">
                                            <h2 style="margin: 0;">💡 Action</h2>
                                            <p style="margin: 0; font-size: 0.9rem;">{recommendation}</p>
                                        </div>
                                        """, unsafe_allow_html=True)

                                    # Additional insights
                                    st.markdown("### 📋 Risk Factors Analysis")
                                    factors = []
                                    if age > 500:
                                        factors.append("🕰️ Very old site - requires special attention")
                                    if disaster_exposure_encoded >= 1:
                                        factors.append("⚠️ Exposed to natural disasters")
                                    if not legal_protection_status:
                                        factors.append("⚖️ Lacks legal protection")
                                    if maintenance_frequency < 2:
                                        factors.append("🔧 Low maintenance frequency")

                                    if factors:
                                        for factor in factors:
                                            st.warning(factor)
                                    else:
                                        st.success("✅ No major risk factors identified")

                                    st.info("💡 Switch to the 'AI Recommendations' tab to get detailed preservation recommendations!")

                st.markdown('</div>', unsafe_allow_html=True)

        # Recommendations Tab
        with tab2:
            st.markdown("### 💡 AI-Powered Preservation Recommendations")

            if 'prediction_results' not in st.session_state:
                st.info("🔮 Please run a risk prediction first to get personalized recommendations.")
                st.markdown("Switch to the 'Risk Prediction' tab and analyze a site to see AI-generated preservation recommendations here.")
            else:
                results = st.session_state.prediction_results

                # Generate recommendations
                recommendations = generate_recommendations(
                    results['risk'],
                    results['climate_factor'],
                    results['age'],
                    results['material_type_encoded'],
                    results['natural_disaster_exposure'],
                    results['legal_protection_status'],
                    results['maintenance_frequency']
                )

                # Display site summary
                st.markdown("#### 🏛️ Site Summary")
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Risk Level", results['risk'])
                with col2:
                    st.metric("Material Type", results['material_type_text'])
                with col3:
                    st.metric("Age (years)", results['age'])

                st.markdown("---")

                # Display recommendations by category
                recommendation_categories = [
                    ("🚨 Immediate Actions", "immediate", "#dc3545"),
                    ("📅 Short-term Plans (1-6 months)", "short_term", "#ffc107"),
                    ("🎯 Long-term Strategy (1-5 years)", "long_term", "#17a2b8"),
                    ("🛡️ Preventive Measures", "preventive", "#28a745"),
                    ("📊 Monitoring & Assessment", "monitoring", "#6f42c1")
                ]

                for title, category, color in recommendation_categories:
                    if recommendations[category]:
                        st.markdown(f"### {title}")
                        st.markdown(f'<div class="metric-card" style="border-left: 4px solid {color};">', unsafe_allow_html=True)

                        for i, rec in enumerate(recommendations[category], 1):
                            st.markdown(f"**{i}.** {rec}")

                        st.markdown('</div>', unsafe_allow_html=True)
                        st.markdown("")

                # Export recommendations
                st.markdown("### 📥 Export Recommendations")
                col1, col2 = st.columns(2)

                with col1:
                    if st.button("📄 Generate PDF Report", use_container_width=True):
                        # Create a simple text report for now
                        report_text = f"""
HERITAGE SITE PRESERVATION RECOMMENDATIONS
Risk Level: {results['risk']}
Material: {results['material_type_text']}
Age: {results['age']} years

"""
                        for title, category, _ in recommendation_categories:
                            if recommendations[category]:
                                report_text += f"\n{title}:\n"
                                for i, rec in enumerate(recommendations[category], 1):
                                    # Remove emoji for PDF
                                    clean_rec = ''.join(char for char in rec if ord(char) < 128)
                                    report_text += f"{i}. {clean_rec}\n"

                        st.download_button(
                            label="💾 Download Report",
                            data=report_text,
                            file_name=f"heritage_recommendations_{results['risk'].lower()}_risk.txt",
                            mime="text/plain"
                        )

                with col2:
                    if st.button("📊 Export to CSV", use_container_width=True):
                        import pandas as pd

                        # Create CSV data
                        csv_data = []
                        for title, category, _ in recommendation_categories:
                            for rec in recommendations[category]:
                                csv_data.append({
                                    'Category': title,
                                    'Recommendation': rec,
                                    'Risk_Level': results['risk'],
                                    'Material': results['material_type_text'],
                                    'Age': results['age']
                                })

                        df = pd.DataFrame(csv_data)
                        csv = df.to_csv(index=False)

                        st.download_button(
                            label="💾 Download CSV",
                            data=csv,
                            file_name=f"heritage_recommendations_{results['risk'].lower()}_risk.csv",
                            mime="text/csv"
                        )

    elif menu == "Analytics & Reports":
        st.markdown('<div class="main-header">', unsafe_allow_html=True)
        st.markdown('<h1>📊 Analytics & Reports</h1>', unsafe_allow_html=True)
        st.markdown('<p>Comprehensive insights and data visualization for heritage sites</p>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)

        stats_df = get_site_statistics()
        sites_df = get_all_sites()

        if not stats_df.empty:
            # Key Metrics Dashboard
            st.markdown("### 📈 Key Metrics")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.markdown('<div class="metric-card">', unsafe_allow_html=True)
                total_sites = stats_df['site_count'].sum()
                st.metric("Total Sites", total_sites, delta="+3 this month")
                st.markdown('</div>', unsafe_allow_html=True)

            with col2:
                st.markdown('<div class="metric-card">', unsafe_allow_html=True)
                total_regions = len(stats_df)
                st.metric("Active Regions", total_regions)
                st.markdown('</div>', unsafe_allow_html=True)

            with col3:
                st.markdown('<div class="metric-card">', unsafe_allow_html=True)
                avg_sites = stats_df['site_count'].mean()
                st.metric("Avg Sites/Region", f"{avg_sites:.1f}")
                st.markdown('</div>', unsafe_allow_html=True)

            with col4:
                st.markdown('<div class="metric-card">', unsafe_allow_html=True)
                top_region = stats_df.iloc[0]['region'] if not stats_df.empty else "N/A"
                st.metric("Top Region", top_region)
                st.markdown('</div>', unsafe_allow_html=True)

            # Charts Section
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("### 📊 Sites by Region")
                st.markdown('<div class="chart-container">', unsafe_allow_html=True)
                fig_bar = px.bar(
                    stats_df,
                    x="region",
                    y="site_count",
                    title="Number of Sites per Region",
                    color="site_count",
                    color_continuous_scale="viridis"
                )
                fig_bar.update_layout(
                    xaxis_title="Region",
                    yaxis_title="Number of Sites",
                    showlegend=False
                )
                st.plotly_chart(fig_bar, use_container_width=True)
                st.markdown('</div>', unsafe_allow_html=True)

            with col2:
                st.markdown("### 🥧 Regional Distribution")
                st.markdown('<div class="chart-container">', unsafe_allow_html=True)
                fig_pie = px.pie(
                    stats_df,
                    names="region",
                    values="site_count",
                    title="Site Distribution by Region"
                )
                fig_pie.update_traces(textposition='inside', textinfo='percent+label')
                st.plotly_chart(fig_pie, use_container_width=True)
                st.markdown('</div>', unsafe_allow_html=True)

            # Trends Analysis
            st.markdown("### 📈 Growth Trends")
            st.markdown('<div class="chart-container">', unsafe_allow_html=True)
            dummy_trends = pd.DataFrame({
                "Date": pd.date_range(start="2023-01-01", periods=12, freq="M"),
                "Sites Added": [5, 8, 6, 7, 9, 10, 12, 15, 14, 13, 16, 18],
                "Cumulative": [5, 13, 19, 26, 35, 45, 57, 72, 86, 99, 115, 133]
            })

            fig_line = px.line(
                dummy_trends,
                x="Date",
                y=["Sites Added", "Cumulative"],
                title="Heritage Sites Registration Trends",
                labels={"value": "Number of Sites", "variable": "Metric"}
            )
            fig_line.update_layout(
                xaxis_title="Date",
                yaxis_title="Number of Sites"
            )
            st.plotly_chart(fig_line, use_container_width=True)
            st.markdown('</div>', unsafe_allow_html=True)

            # Geographic Visualization
            if not sites_df.empty and 'latitude' in sites_df.columns and 'longitude' in sites_df.columns:
                st.markdown("### 🗺️ Geographic Distribution")
                st.markdown('<div class="chart-container">', unsafe_allow_html=True)
                map_df = sites_df.rename(columns={"latitude": "lat", "longitude": "lon"})
                st.map(map_df)
                st.markdown('</div>', unsafe_allow_html=True)

            # Export Section
            st.markdown("### 📥 Export Reports")
            st.markdown('<div class="metric-card">', unsafe_allow_html=True)
            st.markdown("Download comprehensive reports in your preferred format:")

            col1, col2, col3 = st.columns(3)
            with col1:
                if st.button("📊 Export to Excel", use_container_width=True):
                    export_to_excel(stats_df, filename="heritage_analytics_report.xlsx")

            with col2:
                if st.button("📄 Export to PDF", use_container_width=True):
                    export_to_pdf(stats_df, filename="heritage_analytics_report.pdf")

            with col3:
                if st.button("📋 Export Raw Data", use_container_width=True):
                    csv = sites_df.to_csv(index=False)
                    st.download_button(
                        label="💾 Download CSV",
                        data=csv,
                        file_name="heritage_sites_data.csv",
                        mime="text/csv"
                    )

            st.markdown('</div>', unsafe_allow_html=True)

        else:
            st.markdown('<div class="info-box">', unsafe_allow_html=True)
            st.info("📊 No data available for analytics. Add some heritage sites to see insights!")
            st.markdown('</div>', unsafe_allow_html=True)

    elif menu == "Password Management":
        password_management()

    elif menu == "Logout":
        st.session_state.logged_in = False
        st.session_state.show_login = False
        st.success("👋 You have been logged out successfully!")
        st.rerun()

# --- Main App ---
if not st.session_state.logged_in:
    # Landing Page Header
    st.markdown('<div class="main-header">', unsafe_allow_html=True)
    st.markdown('<h1>🏛️ Smart Cultural Heritage System</h1>', unsafe_allow_html=True)
    st.markdown('<p>Preserving History, Protecting Heritage, Powering the Future</p>', unsafe_allow_html=True)
    st.markdown('</div>', unsafe_allow_html=True)

    col1, col2 = st.columns([3, 2])  # Create two columns with a 3:2 width ratio

    with col1:
        # Check if image exists, if not show placeholder
        try:
            st.image("assets/heritage.jpg", use_container_width=True, caption="Cultural Heritage Sites")
        except:
            st.markdown("""
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        height: 400px; border-radius: 10px; display: flex;
                        align-items: center; justify-content: center; color: white;">
                <div style="text-align: center;">
                    <h2>🏛️ Cultural Heritage</h2>
                    <p>Preserving our past for future generations</p>
                </div>
            </div>
            """, unsafe_allow_html=True)

        # Feature highlights
        st.markdown("### ✨ System Features")
        features = [
            "🗺️ **Interactive Site Mapping** - Visualize heritage sites on interactive maps",
            "🧠 **AI Risk Assessment** - Advanced machine learning for site risk prediction",
            "📊 **Analytics Dashboard** - Comprehensive insights and reporting tools",
            "👥 **User Management** - Role-based access control and user administration",
            "📱 **Responsive Design** - Access from any device, anywhere",
            "🔒 **Secure Authentication** - Protected access with encrypted passwords"
        ]

        for feature in features:
            st.markdown(f"- {feature}")

    with col2:
        login_form()  # Display the login form in the right column

        # Quick stats or info
        st.markdown("### 📈 System Overview")
        st.markdown('<div class="info-box">', unsafe_allow_html=True)
        st.markdown("""
        **🎯 Mission**: To provide a comprehensive digital platform for managing,
        monitoring, and preserving cultural heritage sites using cutting-edge technology.

        **🔬 Technology**: Powered by AI/ML algorithms for predictive analytics
        and risk assessment.

        **🌍 Impact**: Supporting heritage conservation efforts worldwide.
        """)
        st.markdown('</div>', unsafe_allow_html=True)

else:
    dashboard()
