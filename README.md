# 🏛️ Smart Cultural Heritage System

A comprehensive web application for managing, monitoring, and preserving cultural heritage sites using advanced AI/ML technology.

## ✨ Features

- **🗺️ Interactive Site Mapping**: Visualize heritage sites on interactive maps
- **🧠 AI Risk Assessment**: Advanced machine learning for site risk prediction
- **📊 Analytics Dashboard**: Comprehensive insights and reporting tools
- **👥 User Management**: Role-based access control and user administration
- **📱 Responsive Design**: Access from any device, anywhere
- **🔒 Secure Authentication**: Protected access with encrypted passwords

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- PostgreSQL database
- Git (optional)

### Installation

1. **Clone or download the repository**
   ```bash
   git clone <repository-url>
   cd CultureApp
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements_clean.txt
   ```

3. **Set up the database**
   - Create a PostgreSQL database named `culture`
   - Update database credentials in `db.py` if needed
   - Run the table creation script:
     ```bash
     python alterTable.py
     ```

4. **Train the AI model**
   ```bash
   python train_model.py
   ```

5. **Test the setup**
   ```bash
   python test_app.py
   ```

6. **Run the application**
   ```bash
   streamlit run app.py
   ```

## 📋 System Requirements

### Dependencies
- streamlit>=1.28.0
- pandas>=1.5.0
- bcrypt>=4.0.0
- psycopg2-binary>=2.9.0
- sqlalchemy>=1.4.0
- plotly>=5.0.0
- fpdf>=2.5.0
- joblib>=1.3.0
- scikit-learn>=1.3.0
- numpy>=1.24.0
- pillow>=9.0.0

### Database Schema
The system requires the following PostgreSQL tables:
- `users`: User authentication and role management
- `sites`: Cultural heritage site information

## 🎯 Usage

### Login
- Default admin credentials can be created using `addUserManaual.py`
- Users can be managed through the admin interface

### Adding Sites
1. Navigate to "Add New Site" (admin only)
2. Fill in site details including coordinates
3. Submit to add to the database

### Risk Assessment
1. Go to "Predict Site Risk"
2. Enter site parameters
3. Get AI-powered risk assessment with confidence scores

### Analytics
- View comprehensive analytics and reports
- Export data in Excel, PDF, or CSV formats
- Interactive charts and visualizations

## 🛠️ Development

### File Structure
```
CultureApp/
├── app.py                 # Main Streamlit application
├── db.py                  # Database connection and queries
├── train_model.py         # AI model training script
├── retrain_model.py       # Model retraining script
├── test_app.py           # Test suite
├── alterTable.py         # Database schema setup
├── addUserManaual.py     # Manual user creation
├── requirements_clean.txt # Clean dependencies list
├── models/               # AI model storage
├── data/                 # Data files
└── assets/              # Static assets
```

### Customization
- Update database credentials in `db.py`
- Modify AI model parameters in `train_model.py`
- Customize styling in the CSS section of `app.py`

## 🔧 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure PostgreSQL is running
   - Check database credentials in `db.py`
   - Verify database `culture` exists

2. **Model Loading Error**
   - Run `python train_model.py` to create the model
   - Check if `models/` directory exists

3. **Import Errors**
   - Install dependencies: `pip install -r requirements_clean.txt`
   - Use virtual environment for clean installation

### Testing
Run the test suite to verify everything is working:
```bash
python test_app.py
```

## 📊 Features Overview

### User Roles
- **Admin**: Full access including user management and site creation
- **User**: View sites, risk prediction, and analytics

### AI Risk Assessment
The system uses machine learning to assess heritage site risks based on:
- Geographic location
- Environmental factors
- Site characteristics
- Maintenance history
- Legal protection status

### Analytics Dashboard
- Site distribution by region
- Growth trends over time
- Interactive visualizations
- Export capabilities

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section
- Run the test suite
- Review the documentation

---

**Built with ❤️ for cultural heritage preservation**
