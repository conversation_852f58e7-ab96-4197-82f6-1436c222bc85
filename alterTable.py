import psycopg2

# Database connection settings
conn = psycopg2.connect(
    dbname='culture',
    user='postgres',
    password='alfred',
    host='localhost',  # or your actual DB host
    port='5432'        # default PostgreSQL port
)

cursor = conn.cursor()

# Define desired columns and types
columns_to_add = {
    "site_name": "TEXT",
    "latitude": "REAL",
    "longitude": "REAL",
    "site_type": "TEXT",
    "region": "TEXT",
    "climate_zone": "TEXT",
    "temperature_avg": "REAL",
    "rainfall_avg": "REAL",
    "age_years": "INTEGER",
    "description": "TEXT",
    "visitor_traffic_level": "TEXT",
    "accessibility": "TEXT",
    "preservation_state": "TEXT",
    "risk_level": "TEXT"
}

# Get existing column names from the sites table
cursor.execute("""
    SELECT column_name
    FROM information_schema.columns
    WHERE table_name = 'sites';
""")
existing_columns = {row[0] for row in cursor.fetchall()}

# Add missing columns
for column, col_type in columns_to_add.items():
    if column not in existing_columns:
        alter_query = f'ALTER TABLE sites ADD COLUMN "{column}" {col_type};'
        print(f"Adding column: {column}")
        cursor.execute(alter_query)

# Commit and close
conn.commit()
cursor.close()
conn.close()

print("All missing columns added successfully.👌")
