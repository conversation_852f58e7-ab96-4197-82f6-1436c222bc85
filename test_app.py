#!/usr/bin/env python3
"""
Test script for the Cultural Heritage System
This script performs basic functionality tests
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import streamlit as st
        print("✅ Streamlit imported successfully")
    except ImportError as e:
        print(f"❌ Streamlit import failed: {e}")
        return False
    
    try:
        import pandas as pd
        print("✅ Pandas imported successfully")
    except ImportError as e:
        print(f"❌ Pandas import failed: {e}")
        return False
    
    try:
        import bcrypt
        print("✅ Bcrypt imported successfully")
    except ImportError as e:
        print(f"❌ Bcrypt import failed: {e}")
        return False
    
    try:
        import plotly.express as px
        print("✅ Plotly imported successfully")
    except ImportError as e:
        print(f"❌ Plotly import failed: {e}")
        return False
    
    try:
        import joblib
        print("✅ Joblib imported successfully")
    except ImportError as e:
        print(f"❌ Joblib import failed: {e}")
        return False
    
    return True

def test_file_structure():
    """Test if required files and directories exist"""
    print("\n📁 Testing file structure...")
    
    required_files = [
        "app.py",
        "db.py",
        "models/risk_predictor_model.joblib",
        "data/Cultural_Heritage_data.csv"
    ]
    
    required_dirs = [
        "models",
        "data",
        "assets"
    ]
    
    all_good = True
    
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✅ Directory '{directory}' exists")
        else:
            print(f"⚠️ Directory '{directory}' missing")
            all_good = False
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ File '{file_path}' exists")
        else:
            print(f"⚠️ File '{file_path}' missing")
            if file_path == "models/risk_predictor_model.joblib":
                print("   💡 Run 'python train_model.py' to create the model")
            all_good = False
    
    return all_good

def test_database_connection():
    """Test database connection"""
    print("\n🗄️ Testing database connection...")
    
    try:
        from db import get_db_connection
        conn = get_db_connection()
        conn.close()
        print("✅ Database connection successful")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("   💡 Make sure PostgreSQL is running and database 'culture' exists")
        return False

def main():
    """Main test function"""
    print("🏛️ Cultural Heritage System - Test Suite")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # Test imports
    if test_imports():
        tests_passed += 1
    
    # Test file structure
    if test_file_structure():
        tests_passed += 1
    
    # Test database connection
    if test_database_connection():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! The application should work correctly.")
        print("🚀 Run 'streamlit run app.py' to start the application")
    else:
        print("⚠️ Some tests failed. Please fix the issues before running the app.")
        
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
